import React from 'react'
import { Metadata } from 'next'
import HeaderSection from './components/HeaderSection'
import WhyChooseSection from './components/WhyChooseSection'
import UseCaseSection from './components/UseCaseSection'
import HowToGuideSection from './components/HowToGuideSection'
import TestimonialSection from './components/TestimonialSection'
import FAQSection from './components/FAQSection'

export async function generateMetadata(): Promise<Metadata> {
  return {
    title: 'AI Image Extender & Outpainting Tool to AI Expand Image | ImgGen',
    description:
      'Use our free AI image extender for seamless generative fill. This advanced AI expand image tool helps you make pictures larger effortlessly and creatively.',
    keywords:
      'ai image extender, ai image outpainting, ai expand image, generative fill, make picture larger, picture expander',
    openGraph: {
      title: 'AI Image Extender & Outpainting Tool to AI Expand Image | ImgGen',
      description:
        'Use our free AI image extender for seamless generative fill. This advanced AI expand image tool helps you make pictures larger effortlessly and creatively.',
      url: 'https://imggen.ai/ai-image-extender',
      images: [
        {
          url: 'https://imggen.ai/images/og-image-extender.jpg',
          width: 1200,
          height: 630,
          alt: 'AI Image Extender Tool',
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: 'AI Image Extender & Outpainting Tool to AI Expand Image | ImgGen',
      description:
        'Use our free AI image extender for seamless generative fill. This advanced AI expand image tool helps you make pictures larger effortlessly and creatively.',
      images: ['https://imggen.ai/images/twitter-card-extender.jpg'],
    },
  }
}

const ImageExtenderPage = () => {
  // 统一配置跳转URL
  const AI_IMAGE_EXTENDER_TOOL_URL = '/ai/image-extender'

  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: 'AI Image Extender & Outpainting Tool | ImgGen',
    description:
      'Use our free AI image extender for seamless generative fill. This advanced AI expand image tool helps you make pictures larger effortlessly and creatively.',
    url: 'https://www.imggen.org/tools/image-extenter',
    applicationCategory: 'DesignApplication',
    operatingSystem: 'Web',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
      availability: 'https://schema.org/InStock',
    },
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.8',
      reviewCount: '1250',
    },
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': 'https://www.imggen.org/tools/image-extenter',
    },
  }

  return (
    <div className="min-h-screen bg-slate-900">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />

      {/* Header Section */}
      <HeaderSection toolUrl={AI_IMAGE_EXTENDER_TOOL_URL} />

      {/* Main Content */}
      <WhyChooseSection toolUrl={AI_IMAGE_EXTENDER_TOOL_URL} />
      <UseCaseSection toolUrl={AI_IMAGE_EXTENDER_TOOL_URL} />
      <HowToGuideSection toolUrl={AI_IMAGE_EXTENDER_TOOL_URL} />
      <TestimonialSection toolUrl={AI_IMAGE_EXTENDER_TOOL_URL} />
      <FAQSection toolUrl={AI_IMAGE_EXTENDER_TOOL_URL} />
    </div>
  )
}

export default ImageExtenderPage
