'use client'

import React from 'react'
import { Star } from 'lucide-react'

const TestimonialSection: React.FC = () => {
  const testimonials = [
    {
      id: 1,
      content:
        "I've been using YouCam AI Image Extender for a few weeks now, and it's been a game-changer for my projects. The tool seamlessly extends images without losing quality, and with over 20 size options, it's perfect for creating larger prints. Highly recommend it for anyone in the design field!",
      author: '<PERSON>',
      role: 'Graphic Designer',
      avatar:
        'https://images.unsplash.com/photo-1494790108755-2616c9c0e8e3?w=100&h=100&fit=crop&crop=face',
      rating: 5,
    },
    {
      id: 2,
      content:
        "As a social media manager, I often need to resize images for different platforms. YouCam AI Image Extender has made this process so much easier. The extended images look natural and professional, and the ability to resize for IG, FB, and TikTok saves me a ton of time. It's definitely worth trying out!",
      author: '<PERSON>',
      role: 'Social Media Manager',
      avatar:
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
      rating: 5,
    },
    {
      id: 3,
      content:
        "YouCam AI Image Extender is a fantastic tool for photographers. It effortlessly extends the background of my photos, allowing for more creative compositions. The AI does an excellent job of maintaining the original quality, and with over 20 size options, it's incredibly versatile. Love it!",
      author: 'Sarah Patel',
      role: 'Photographer',
      avatar:
        'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
      rating: 5,
    },
  ]

  return (
    <section className="py-20 bg-slate-900 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-32 h-32 bg-blue-500/10 rounded-full blur-2xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-blue-500/10 rounded-full blur-2xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-6xl mx-auto">
          {/* 标题部分 */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-blue-500/10 rounded-full px-4 py-2 mb-6 border border-blue-500/20">
              <Star className="w-4 h-4 text-yellow-400" />
              <span className="text-blue-300 text-sm font-medium">
                User Reviews
              </span>
            </div>

            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6">
              See What Our Users Think About Our{' '}
              <span className="text-blue-400">AI Image Extender</span>!
            </h2>

            <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed mb-8">
              Discover the amazing feedback from our users about our AI Image
              Extender!
            </p>

            {/* 评分展示 */}
            <div className="flex items-center justify-center gap-4 mb-8">
              <div className="text-center">
                <div className="text-3xl font-bold text-yellow-400 mb-1">
                  ★★★★★ 4.9/5 (934,534 reviews)
                </div>
                <div className="text-gray-400 text-lg font-medium">
                  Tool Rating
                </div>
              </div>
            </div>
          </div>

          {/* 评价卡片网格 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div
                key={testimonial.id}
                className="bg-slate-800 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-slate-700"
              >
                {/* 用户头像和信息 */}
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-16 h-16 rounded-full overflow-hidden ring-2 ring-blue-400/30">
                    <img
                      src={testimonial.avatar}
                      alt={testimonial.author}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div>
                    <h4 className="font-bold text-white">
                      {testimonial.author}
                    </h4>
                    <p className="text-gray-400 text-sm">{testimonial.role}</p>
                  </div>
                </div>

                {/* 评分 */}
                <div className="flex items-center gap-1 mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star
                      key={i}
                      className="w-4 h-4 text-yellow-400 fill-current"
                    />
                  ))}
                </div>

                {/* 评价内容 */}
                <blockquote className="text-gray-300 leading-relaxed">
                  "{testimonial.content}"
                </blockquote>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

export default TestimonialSection
