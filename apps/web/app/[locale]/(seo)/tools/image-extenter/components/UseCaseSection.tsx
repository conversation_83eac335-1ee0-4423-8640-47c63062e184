'use client'

import React from 'react'
import {
  ArrowRight,
  Maximize2,
  RotateCcw,
  Monitor,
  ShoppingBag,
} from 'lucide-react'
import { Link } from '@i18n/routing'

interface UseCaseSectionProps {
  toolUrl: string
}

const UseCaseSection: React.FC<UseCaseSectionProps> = ({ toolUrl }) => {
  const useCases = [
    {
      id: 1,
      icon: <Maximize2 className="w-6 h-6" />,
      title: 'Turn Any Portrait into a Landscape to Make Picture Larger',
      description:
        'Have a great vertical shot but need it in landscape? Our ai expand image tool is the perfect picture expander. It intelligently generates a wider background, turning your portrait photos into stunning 16:9 banners or thumbnails in seconds.',
      image:
        'https://images.unsplash.com/photo-*************-2616c9c0e8e3?w=600&h=400&fit=crop',
      alt: 'A portrait photo of a woman on a beach expanded into a landscape using the AI image extender to make the picture larger.',
      buttonText: '立即转换您的照片',
      layout: 'left-image',
      gradient: 'from-cyan-500 to-blue-600',
    },
    {
      id: 2,
      icon: <RotateCcw className="w-6 h-6" />,
      title: 'How to Enlarge a Picture and Fix Awkward Framing',
      description:
        "Don't discard awkwardly framed photos. Use our ai image extender to add more space around your subject. The generative fill technology corrects compositions, saving you from having to retake the shot. Over 80% of users report saving previously unusable photos.",
      image:
        'https://images.unsplash.com/photo-1511367461989-f85a21fda167?w=600&h=400&fit=crop',
      alt: 'A poorly framed photo is fixed using generative fill to recenter the subject with our AI image extender.',
      buttonText: '立即修复您的构图',
      layout: 'right-image',
      gradient: 'from-purple-500 to-pink-600',
    },
    {
      id: 3,
      icon: <Monitor className="w-6 h-6" />,
      title: 'Create Stunning Wallpapers with Our AI Art Image Expander Free',
      description:
        'Expand your AI-generated art or favorite images to fit any screen. Our ai image outpainting tool is perfect for creating unique desktop wallpapers or social media banners. The ai fill in image feature ensures the style remains consistent.',
      image:
        'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=600&h=400&fit=crop',
      alt: 'An AI-generated artwork being expanded into a widescreen wallpaper with our ai art image expander free tool.',
      buttonText: '立即创作您的壁纸',
      layout: 'left-image',
      gradient: 'from-green-500 to-teal-600',
    },
    {
      id: 4,
      icon: <ShoppingBag className="w-6 h-6" />,
      title: 'Enhance Product Shots with a Creative AI Extender',
      description:
        'Make your product images pop. Use our ai expand image tool to add creative backgrounds or extend simple ones for a more professional look. This ai image enhancement technique is used by e-commerce owners to increase engagement by up to 30%.',
      image:
        'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=600&h=400&fit=crop',
      alt: 'A product photo being enhanced with a creative background using our AI extender technology.',
      buttonText: '立即美化您的产品图',
      layout: 'right-image',
      gradient: 'from-orange-500 to-red-600',
    },
  ]

  return (
    <section className="py-20 bg-slate-900 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-1/4 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-1/4 w-48 h-48 bg-blue-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-6xl mx-auto">
          {/* 标题部分 */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-blue-500/10 rounded-full px-4 py-2 mb-6 border border-blue-500/20">
              <Maximize2 className="w-4 h-4 text-blue-400" />
              <span className="text-blue-300 text-sm font-medium">
                Use Cases
              </span>
            </div>

            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Real-World Applications for{' '}
              <span className="text-blue-400">AI Image Outpainting</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
              See how our{' '}
              <span className="text-blue-400 font-semibold">
                ai image outpainting technology
              </span>{' '}
              can transform your visuals. Using our{' '}
              <span className="text-blue-400 font-semibold">
                ai fill in image feature
              </span>{' '}
              has helped over{' '}
              <span className="text-blue-400 font-bold">500,000 users</span>{' '}
              repurpose their content for social media, marketing, and art.
            </p>
          </div>

          {/* 用例展示 */}
          <div className="space-y-16">
            {useCases.map((useCase, index) => (
              <div
                key={useCase.id}
                className={`group grid md:grid-cols-2 gap-8 items-center ${
                  useCase.layout === 'right-image'
                    ? 'md:grid-flow-col-dense'
                    : ''
                }`}
              >
                {/* 图片部分 */}
                <div
                  className={`relative ${
                    useCase.layout === 'right-image' ? 'md:col-start-2' : ''
                  }`}
                >
                  <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-sm border border-slate-700/50 group-hover:border-cyan-500/50 transition-all duration-500">
                    <img
                      src={useCase.image}
                      alt={useCase.alt}
                      className="w-full aspect-video object-cover transform transition duration-700 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-black/20"></div>

                    {/* 悬浮效果 */}
                    <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  </div>
                </div>

                {/* 内容部分 */}
                <div
                  className={`space-y-6 ${
                    useCase.layout === 'right-image'
                      ? 'md:col-start-1 md:row-start-1'
                      : ''
                  }`}
                >
                  <div className="flex items-center gap-4">
                    <div
                      className={`w-12 h-12 bg-gradient-to-br ${useCase.gradient} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}
                    >
                      {useCase.icon}
                    </div>
                    <div className="w-8 h-0.5 bg-gradient-to-r from-cyan-400 to-purple-400 group-hover:w-12 transition-all duration-300"></div>
                  </div>

                  <h3 className="text-2xl md:text-3xl font-bold text-white leading-tight group-hover:text-cyan-400 transition-colors duration-300">
                    {useCase.title}
                  </h3>

                  <p className="text-gray-300 text-lg leading-relaxed">
                    {useCase.description}
                  </p>

                  <Link
                    href={toolUrl}
                    className={`inline-flex items-center gap-3 px-6 py-3 bg-gradient-to-r ${useCase.gradient} text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 group/btn`}
                  >
                    <span>{useCase.buttonText}</span>
                    <ArrowRight className="w-4 h-4 group-hover/btn:translate-x-1 transition-transform duration-300" />
                  </Link>
                </div>
              </div>
            ))}
          </div>

          {/* 统计数据 */}
          <div className="mt-20 grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center p-6 bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-sm rounded-2xl border border-slate-700/50">
              <div className="text-3xl font-bold text-cyan-400 mb-2">
                500,000+
              </div>
              <div className="text-gray-300">Users Served</div>
            </div>
            <div className="text-center p-6 bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-sm rounded-2xl border border-slate-700/50">
              <div className="text-3xl font-bold text-purple-400 mb-2">80%</div>
              <div className="text-gray-300">Photos Saved</div>
            </div>
            <div className="text-center p-6 bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-sm rounded-2xl border border-slate-700/50">
              <div className="text-3xl font-bold text-pink-400 mb-2">30%</div>
              <div className="text-gray-300">Engagement Increase</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default UseCaseSection
