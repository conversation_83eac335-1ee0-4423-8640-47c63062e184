'use client'

import React from 'react'
import { <PERSON>, <PERSON>ap, Shield, ArrowRight } from 'lucide-react'
import { Link } from '@i18n/routing'
import { useTranslations } from 'next-intl'

interface WhyChooseSectionProps {
  toolUrl: string
}

const WhyChooseSection: React.FC<WhyChooseSectionProps> = ({ toolUrl }) => {
  const t = useTranslations('imageExtenter')
  return (
    <section className="py-20 bg-slate-900 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute top-10 right-10 w-40 h-40 bg-blue-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-10 left-10 w-32 h-32 bg-blue-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-6xl mx-auto">
          {/* 标题部分 */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-blue-500/10 rounded-full px-4 py-2 mb-6 border border-blue-500/20">
              <Brain className="w-4 h-4 text-blue-400" />
              <span className="text-blue-300 text-sm font-medium">
                {t('whyChooseBadge')}
              </span>
            </div>

            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              {t('whyChooseTitle')}
            </h2>
            <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
              {t('whyChooseDescription')}
            </p>
          </div>

          {/* 对比图片区域 */}
          <div className="mb-16">
            <div className="bg-slate-800 rounded-2xl p-8 border border-slate-700 shadow-xl">
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-white mb-2">
                  {t('seeDifferenceTitle')}
                </h3>
                <p className="text-gray-400">{t('seeDifferenceDescription')}</p>
              </div>

              <div className="grid md:grid-cols-3 gap-8 items-center">
                {/* Before图片 */}
                <div className="relative">
                  <div className="absolute -top-3 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold z-10">
                    {t('beforeLabel')}
                  </div>
                  <div className="relative overflow-hidden rounded-xl border-2 border-red-200/20">
                    <img
                      src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop&crop=face"
                      alt="A left-right comparison image. Left side shows a poorly cropped photo with the person's head or feet cut off"
                      className="w-full aspect-[4/3] object-cover"
                    />
                    <div className="absolute inset-0 bg-black/20"></div>
                  </div>
                  <div className="mt-3 text-center">
                    <p className="text-gray-400 text-sm">
                      {t('poorlyCroppedText')}
                    </p>
                  </div>
                </div>

                {/* 箭头指示 */}
                <div className="flex justify-center">
                  <div className="flex flex-col items-center gap-2">
                    <ArrowRight className="w-8 h-8 text-blue-400" />
                    <span className="text-blue-400 text-sm font-semibold">
                      {t('aiMagicText')}
                    </span>
                  </div>
                </div>

                {/* After图片 */}
                <div className="relative">
                  <div className="absolute -top-3 left-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold z-10">
                    {t('afterLabel')}
                  </div>
                  <div className="relative overflow-hidden rounded-xl border-2 border-green-200/20">
                    <img
                      src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=600&h=300&fit=crop"
                      alt="Right side shows the complete image after using ImgGen, AI intelligently completing the missing parts with perfect composition"
                      className="w-full aspect-[4/3] object-cover"
                    />
                    <div className="absolute inset-0 bg-black/20"></div>

                    {/* AI扩展效果指示 */}
                    <div className="absolute inset-0 border-2 border-blue-400/50 rounded-xl"></div>
                  </div>
                  <div className="mt-3 text-center">
                    <p className="text-blue-400 text-sm font-semibold">
                      {t('aiEnhancedText')}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 优势特点 */}
          <div className="grid md:grid-cols-3 gap-6">
            {/* 第一个优势 */}
            <div className="bg-slate-800 rounded-xl p-6 border border-slate-700 hover:border-blue-500/50 transition-colors duration-200">
              <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mb-4">
                <Brain className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-bold text-white mb-3">
                {t('contextAwareTitle')}
              </h3>
              <p className="text-gray-300 leading-relaxed">
                {t('contextAwareDescription')}
              </p>
            </div>

            {/* 第二个优势 */}
            <div className="bg-slate-800 rounded-xl p-6 border border-slate-700 hover:border-blue-500/50 transition-colors duration-200">
              <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mb-4">
                <Zap className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-bold text-white mb-3">
                {t('oneClickTitle')}
              </h3>
              <p className="text-gray-300 leading-relaxed">
                {t('oneClickDescription')}
              </p>
            </div>

            {/* 第三个优势 */}
            <div className="bg-slate-800 rounded-xl p-6 border border-slate-700 hover:border-blue-500/50 transition-colors duration-200">
              <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mb-4">
                <Shield className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-bold text-white mb-3">
                Secure & Private
              </h3>
              <p className="text-gray-300 leading-relaxed">
                All your images are securely processed with full privacy
                protection. We guarantee simplicity, efficiency, and complete
                data security.
              </p>
            </div>
          </div>

          {/* CTA按钮 */}
          <div className="text-center mt-12">
            <Link
              href={toolUrl}
              className="inline-flex items-center gap-3 px-8 py-4 bg-blue-600 text-white font-semibold rounded-xl hover:bg-blue-700 transition-colors duration-200"
            >
              <Shield className="w-5 h-5" />
              <span>Try AI Image Extender Free</span>
              <ArrowRight className="w-5 h-5" />
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}

export default WhyChooseSection
